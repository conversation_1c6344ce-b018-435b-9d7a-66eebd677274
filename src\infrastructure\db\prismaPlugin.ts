// A Fastify plugin that provides a single PrismaClient instance to the app.
// It decorates fastify as `app.prisma` and disconnects cleanly on shutdown.
import fp from "fastify-plugin";
import { PrismaClient } from "@prisma/client";
export default fp(async (app) => {
  // Create one PrismaClient for the whole app lifecycle
  const prisma = new PrismaClient();
  // Make it available as app.prisma
  app.decorate("prisma", prisma);
  // Ensure the DB connection is closed when Fastify shuts down
  app.addHook("onClose", async (server) => {
    await server.prisma.$disconnect();
  });
});